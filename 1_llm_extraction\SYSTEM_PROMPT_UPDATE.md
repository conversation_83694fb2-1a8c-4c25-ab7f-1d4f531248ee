# 系統提示詞更新說明

## 🔄 更新內容

根據您的要求，我們已經將系統提示詞從 `lmm_extraction.json` 檔案中移除，並替換為您提供的專業版本。

## ✅ 主要變更

### 1. **系統提示詞來源變更**
- **之前**：從 `lmm_extraction.json` 檔案讀取
- **現在**：直接內建在 `agno_llm_app.py` 中

### 2. **新的系統提示詞特色**
- 📋 **更詳細的角色定義**：明確定義為經驗豐富的理賠專家
- 🎯 **具體的欄位說明**：每個欄位都有詳細的提取指引和範例
- 📊 **標準化輸出格式**：提供完整的 JSON 模板
- 🔍 **複雜度分類標準**：詳細的簡單/複雜案例分類準則
- 💡 **實際範例**：包含完整的輸入輸出範例

### 3. **檔案結構簡化**
- ❌ 不再需要 `lmm_extraction.json` 檔案
- ✅ 系統提示詞直接內建在應用程式中
- 🔧 簡化了設定和部署流程

## 📋 新系統提示詞內容概覽

### 角色定義
```
You are an experienced "Claims Expert". Your primary responsibility is to precisely extract critical features from the `claim_description` provided by the policyholder, which are essential for making claims judgments.
```

### 主要任務
- 從理賠描述中提取 17 個關鍵特徵
- 組織成標準化的 JSON 格式
- 提供準確的分類和評估

### 提取欄位（17個）
1. **accident_type** - 事故類型
2. **time_of_incident** - 事故時間
3. **location_of_incident** - 事故地點
4. **self_role** - 投保人角色
5. **self_damage_status** - 損害狀態
6. **third_party_count** - 第三方數量
7. **third_party_details** - 第三方詳情
8. **property_damage_description** - 財產損害描述
9. **personal_injury_description** - 人身傷害描述
10. **incident_narrative** - 事故經過
11. **potential_liability_third_party_info** - 責任資訊
12. **reporting_medical_status** - 報案狀態
13. **environmental_factors** - 環境因素
14. **other_supporting_documents_mentioned** - 支持文件
15. **emotional_tone** - 情緒語調
16. **severity_attitude** - 嚴重性態度
17. **case_complexity** - 案件複雜度
18. **case_complexity_description** - 複雜度說明

### 特殊功能
- **情緒語調分析**：6種分類選項
- **嚴重性態度評估**：3種分類選項
- **案件複雜度判斷**：詳細的分類標準和評估因素

## 🔧 技術實作

### 程式碼變更
```python
def _get_professional_system_prompt(self) -> str:
    """
    專業的系統提示詞
    """
    return """## Role Definition
    You are an experienced "Claims Expert"...
    """
```

### 配置簡化
- 移除了對 `JSON_CONFIG_PATH` 的依賴
- 簡化了 `config.py` 設定
- 更新了 `setup.py` 檢查邏輯

## 🎯 優勢

### 1. **更高準確度**
- 詳細的欄位說明提高提取準確度
- 標準化的分類選項確保一致性
- 完整的範例指導模型行為

### 2. **更好的維護性**
- 系統提示詞直接在程式碼中
- 版本控制更容易
- 部署更簡單

### 3. **更強的專業性**
- 基於理賠專家的實際需求設計
- 包含業界標準的分類方法
- 考慮了法規和合規要求

## 🚀 使用方式

### 無需變更
現有的使用方式完全不變：

```bash
# 測試
python test_single_claim.py

# 執行
python agno_llm_app.py
```

### 自動生效
新的系統提示詞會自動應用到所有處理中，無需額外設定。

## 📊 預期效果

### 提取品質提升
- 更準確的欄位識別
- 更一致的分類結果
- 更詳細的複雜度分析

### 處理效率
- 減少模型混淆
- 提高 JSON 解析成功率
- 降低重試次數

## 🔍 驗證方式

您可以透過以下方式驗證新系統提示詞的效果：

```bash
# 1. 執行演示
python demo.py

# 2. 測試單一案例
python test_single_claim.py

# 3. 檢查提取結果的品質和一致性
```

## 📝 總結

這次更新將系統提示詞從外部 JSON 檔案移至內建，並採用了您提供的專業版本。新的提示詞更加詳細、專業，預期將顯著提升特徵提取的準確度和一致性。

所有現有功能保持不變，但提取品質將得到顯著改善。🎉
