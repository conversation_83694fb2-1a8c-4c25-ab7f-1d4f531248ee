"""
Agno LLM 理賠分析應用程式

此應用程式使用 Agno framework 從保險理賠描述中提取關鍵特徵，
使用大型語言模型 (LLM) 處理 CSV 檔案中的理賠案例，並將提取的特徵寫回同一檔案。
"""

import json
import time
import logging
from typing import Dict, Any
from tqdm import tqdm

from agno.agent import Agent
from agno.models.openai import OpenAIChat
from agno.models.anthropic import Claude
from agno.models.google import GoogleGemini

import config
from utils import (
    load_csv_data, 
    save_csv_data, 
    initialize_output_columns,
    parse_llm_response,
    update_dataframe_row,
    get_unprocessed_claims
)

# 設定日誌記錄
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class ClaimsAnalysisAgent:
    """
    使用 Agno framework 分析保險理賠的專門代理程式
    """

    def __init__(self, model_name: str = config.DEFAULT_MODEL):
        """
        初始化理賠分析代理程式

        Args:
            model_name: 要使用的模型名稱 (例如: "gemini-2.5-flash-preview-05-20", "gpt-4o", "claude-3-sonnet-20240229")
        """
        self.model_name = model_name
        self.agent = self._create_agent()
        
    def _create_agent(self) -> Agent:
        """
        建立並配置具有理賠分析提示詞的 Agno 代理程式

        Returns:
            已配置的 Agno 代理程式
        """
        # 從 JSON 配置載入系統提示詞
        system_prompt = self._load_system_prompt()

        # 選擇適當的模型
        if "gemini" in self.model_name.lower():
            model = GoogleGemini(id=self.model_name)
        elif "gpt" in self.model_name.lower():
            model = OpenAIChat(id=self.model_name)
        elif "claude" in self.model_name.lower():
            model = Claude(id=self.model_name)
        else:
            # 預設使用 Gemini
            model = GoogleGemini(id="gemini-2.5-flash-preview-05-20")
            
        # 建立代理程式
        agent = Agent(
            model=model,
            instructions=[
                system_prompt,
                "請僅以有效的 JSON 格式回應。",
                "請勿在 JSON 之外包含任何說明或額外文字。",
                "如果資訊不可用，請使用 null 作為值。"
            ],
            description="專門從保險理賠描述中提取關鍵特徵的專家理賠分析師",
            markdown=False
        )

        return agent
    
    def _load_system_prompt(self) -> str:
        """
        從 JSON 配置檔案載入系統提示詞

        Returns:
            系統提示詞字串
        """
        try:
            with open(config.JSON_CONFIG_PATH, 'r', encoding='utf-8') as f:
                json_data = json.load(f)

            # 從 Claims Feature Extraction Expert 節點提取系統訊息
            for node in json_data.get('nodes', []):
                if node.get('name') == 'Claims Feature Extraction Expert':
                    parameters = node.get('parameters', {})
                    options = parameters.get('options', {})
                    system_message = options.get('systemMessage', '')
                    if system_message.startswith('='):
                        system_message = system_message[1:]  # 移除開頭的 '='
                    return system_message

            # 如果在 JSON 中找不到，則使用備用系統提示詞
            return self._get_fallback_system_prompt()

        except Exception as e:
            logger.warning(f"無法從 JSON 載入系統提示詞: {e}")
            return self._get_fallback_system_prompt()
    
    def _get_fallback_system_prompt(self) -> str:
        """
        JSON 載入失敗時的備用系統提示詞

        Returns:
            備用系統提示詞字串
        """
        return """## 角色定義
您是一位經驗豐富的「理賠專家」。您的主要職責是精確地從投保人提供的 `claim_description` 中提取關鍵特徵，這些特徵對於理賠判斷至關重要。

## 主要任務
從使用者提供的 claim_description 中識別並提取以下關鍵資訊，然後將它們組織成標準化的 JSON 格式。輸出 JSON 中的不同欄位總數將為 17 個。

## 輸出格式
您的輸出必須是包含以下 17 個鍵值對的單一 JSON 物件。如果描述中未明確提及某些資訊，其值應為 `null`。

**僅輸出 JSON 結構，不要提供任何額外的說明、道歉或評論，即使描述不完整或不清楚。**

提取以下欄位：
- accident_type: 事故性質的分類
- time_of_incident: 事故發生的具體日期和時間
- location_of_incident: 事故發生的詳細地點
- self_role: 投保人在事故中的角色
- self_damage_status: 投保人是否遭受損害或傷害
- third_party_count: 涉及的其他方數量
- third_party_details: 涉及第三方的簡要描述
- property_damage_description: 受損財產的描述
- personal_injury_description: 人身傷害的描述
- incident_narrative: 事故發生過程的摘要
- potential_liability_third_party_info: 第三方責任相關資訊
- reporting_medical_status: 是否報警/尋求醫療治療
- environmental_factors: 事故期間的環境條件
- other_supporting_documents_mentioned: 提及的任何額外證據
- emotional_tone: 描述的整體情緒語調
- severity_attitude: 投保人是否淡化或強調嚴重性
- case_complexity: 基於多個因素判斷為「簡單」或「複雜」
- case_complexity_description: 複雜性分類的簡要說明"""
    
    def analyze_claim(self, claim_description: str) -> Dict[str, Any]:
        """
        分析單一理賠描述並提取特徵

        Args:
            claim_description: 要分析的理賠描述文字

        Returns:
            包含提取特徵的字典
        """
        try:
            # 為代理程式建立提示詞
            prompt = f"理賠描述為：{claim_description}\n\n請開始提取資訊。"

            # 從代理程式取得回應
            response = self.agent.run(prompt)

            # 解析回應
            if hasattr(response, 'content'):
                response_text = response.content
            else:
                response_text = str(response)

            # 從回應中提取 JSON
            extracted_data = parse_llm_response(response_text)

            return extracted_data

        except Exception as e:
            logger.error(f"分析理賠時發生錯誤: {e}")
            return {}
    
    def process_claims_batch(self, claims_df, unprocessed_indices, start_idx: int = 0, batch_size: int = config.BATCH_SIZE) -> None:
        """
        處理一批理賠案例並更新 DataFrame

        Args:
            claims_df: 包含理賠資料的 DataFrame
            unprocessed_indices: 需要處理的索引清單
            start_idx: 在 unprocessed_indices 清單中的起始索引
            batch_size: 此批次要處理的理賠案例數量
        """
        end_idx = min(start_idx + batch_size, len(unprocessed_indices))
        batch_indices = unprocessed_indices[start_idx:end_idx]

        logger.info(f"正在處理批次中的 {len(batch_indices)} 筆理賠案例")

        for idx in tqdm(batch_indices, desc="處理理賠案例"):
            try:
                claim_description = claims_df.loc[idx, 'claim_description']

                # 如果已處理則跳過（雙重檢查）
                if claims_df.loc[idx, 'llm_processed'] == True:
                    continue

                # 分析理賠案例
                extracted_data = self.analyze_claim(claim_description)

                if extracted_data:
                    # 更新 DataFrame
                    update_dataframe_row(claims_df, idx, extracted_data)
                    logger.info(f"成功處理索引 {idx} 的理賠案例")
                else:
                    logger.warning(f"索引 {idx} 的理賠案例未提取到資料")

                # 小延遲以避免速率限制
                time.sleep(0.1)

            except Exception as e:
                logger.error(f"處理索引 {idx} 的理賠案例時發生錯誤: {e}")
                continue

def main():
    """
    執行理賠分析應用程式的主函數
    """
    logger.info("啟動 Agno LLM 理賠分析應用程式")

    # 檢查 API 金鑰
    if not config.OPENAI_API_KEY and not config.ANTHROPIC_API_KEY and not config.GOOGLE_API_KEY:
        logger.error("找不到 API 金鑰。請在 .env 檔案中設定 OPENAI_API_KEY、ANTHROPIC_API_KEY 或 GOOGLE_API_KEY")
        return
    
    try:
        # 載入 CSV 資料
        df = load_csv_data()

        # 初始化輸出欄位
        df = initialize_output_columns(df)

        # 取得未處理的理賠案例
        unprocessed_df = get_unprocessed_claims(df)

        if len(unprocessed_df) == 0:
            logger.info("所有理賠案例都已處理完成！")
            return

        # 取得未處理理賠案例的索引
        unprocessed_indices = unprocessed_df.index.tolist()

        # 建立理賠分析代理程式
        agent = ClaimsAnalysisAgent(model_name=config.DEFAULT_MODEL)

        # 批次處理理賠案例
        total_claims = len(unprocessed_indices)
        for start_idx in range(0, total_claims, config.BATCH_SIZE):
            batch_num = start_idx // config.BATCH_SIZE + 1
            logger.info(f"處理第 {batch_num} 批次，從索引 {start_idx} 開始")

            # 處理批次
            agent.process_claims_batch(df, unprocessed_indices, start_idx, config.BATCH_SIZE)

            # 每批次後儲存進度
            save_csv_data(df)
            logger.info(f"處理第 {batch_num} 批次後已儲存進度")

        logger.info("理賠分析成功完成！")

    except Exception as e:
        logger.error(f"應用程式錯誤: {e}")
        raise

if __name__ == "__main__":
    main()
