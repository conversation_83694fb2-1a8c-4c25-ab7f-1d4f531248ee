"""
Agno LLM Claims Analysis Application

This application uses the Agno framework to extract key features from insurance claim descriptions
using a Large Language Model (LLM). It processes claims from a CSV file and writes the extracted
features back to the same file.
"""

import json
import time
import logging
from typing import Dict, Any, Optional
from tqdm import tqdm

from agno.agent import Agent
from agno.models.openai import OpenAIChat
from agno.models.anthropic import Claude

import config
from utils import (
    load_csv_data, 
    save_csv_data, 
    initialize_output_columns,
    parse_llm_response,
    update_dataframe_row,
    get_unprocessed_claims
)

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class ClaimsAnalysisAgent:
    """
    A specialized agent for analyzing insurance claims using Agno framework
    """
    
    def __init__(self, model_name: str = config.DEFAULT_MODEL):
        """
        Initialize the Claims Analysis Agent
        
        Args:
            model_name: Name of the model to use (e.g., "gpt-4o", "claude-3-sonnet-20240229")
        """
        self.model_name = model_name
        self.agent = self._create_agent()
        
    def _create_agent(self) -> Agent:
        """
        Create and configure the Agno agent with the claims analysis prompt
        
        Returns:
            Configured Agno Agent
        """
        # Load the system prompt from the JSON configuration
        system_prompt = self._load_system_prompt()
        
        # Choose the appropriate model
        if "gpt" in self.model_name.lower():
            model = OpenAIChat(id=self.model_name)
        elif "claude" in self.model_name.lower():
            model = Claude(id=self.model_name)
        else:
            # Default to OpenAI
            model = OpenAIChat(id="gpt-4o")
            
        # Create the agent
        agent = Agent(
            model=model,
            instructions=[
                system_prompt,
                "Always respond with valid JSON format only.",
                "Do not include any explanations or additional text outside the JSON.",
                "If information is not available, use null as the value."
            ],
            description="Expert claims analyst for extracting key features from insurance claim descriptions",
            markdown=False
        )
        
        return agent
    
    def _load_system_prompt(self) -> str:
        """
        Load the system prompt from the JSON configuration file
        
        Returns:
            System prompt string
        """
        try:
            with open(config.JSON_CONFIG_PATH, 'r', encoding='utf-8') as f:
                json_data = json.load(f)
            
            # Extract the system message from the Claims Feature Extraction Expert node
            for node in json_data.get('nodes', []):
                if node.get('name') == 'Claims Feature Extraction Expert':
                    parameters = node.get('parameters', {})
                    options = parameters.get('options', {})
                    system_message = options.get('systemMessage', '')
                    if system_message.startswith('='):
                        system_message = system_message[1:]  # Remove the leading '='
                    return system_message
            
            # Fallback system prompt if not found in JSON
            return self._get_fallback_system_prompt()
            
        except Exception as e:
            logger.warning(f"Could not load system prompt from JSON: {e}")
            return self._get_fallback_system_prompt()
    
    def _get_fallback_system_prompt(self) -> str:
        """
        Fallback system prompt if JSON loading fails
        
        Returns:
            Fallback system prompt string
        """
        return """## Role Definition
You are an experienced "Claims Expert". Your primary responsibility is to precisely extract critical features from the `claim_description` provided by the policyholder, which are essential for making claims judgments.

## Main Task
Identify and extract the following key information from the user-provided claim_description, and then organize them into a standardized JSON format. The total number of distinct fields in the output JSON will be 17.

## Output Format
Your output must be a single JSON object containing the following 17 key-value pairs. If certain information is not explicitly mentioned in the description, its value should be `null`.

**Only output the JSON structure, without providing any additional explanations, apologies, or comments, even if the description is incomplete or unclear.**

Extract the following fields:
- accident_type: Categorization of the nature of the incident
- time_of_incident: The specific date and time the accident occurred
- location_of_incident: Detailed location where the accident occurred
- self_role: The policyholder's role in the incident
- self_damage_status: Whether the policyholder suffered damage or injury
- third_party_count: Number of other parties involved
- third_party_details: Brief description of third parties involved
- property_damage_description: Description of damaged property
- personal_injury_description: Description of personal injuries
- incident_narrative: Summary of how the accident occurred
- potential_liability_third_party_info: Information about third-party liability
- reporting_medical_status: Whether police/medical treatment was sought
- environmental_factors: Environmental conditions during the incident
- other_supporting_documents_mentioned: Any additional evidence mentioned
- emotional_tone: Overall emotional tone of the description
- severity_attitude: Whether the policyholder minimizes or emphasizes severity
- case_complexity: "Simple" or "Complex" based on multiple factors
- case_complexity_description: Brief explanation of complexity classification"""
    
    def analyze_claim(self, claim_description: str) -> Dict[str, Any]:
        """
        Analyze a single claim description and extract features
        
        Args:
            claim_description: The claim description text to analyze
            
        Returns:
            Dictionary containing extracted features
        """
        try:
            # Create the prompt for the agent
            prompt = f"The 'claims_description' is {claim_description}\n\nplease start to extract information."
            
            # Get response from the agent
            response = self.agent.run(prompt)
            
            # Parse the response
            if hasattr(response, 'content'):
                response_text = response.content
            else:
                response_text = str(response)
            
            # Extract JSON from response
            extracted_data = parse_llm_response(response_text)
            
            return extracted_data
            
        except Exception as e:
            logger.error(f"Error analyzing claim: {e}")
            return {}
    
    def process_claims_batch(self, claims_df, unprocessed_indices, start_idx: int = 0, batch_size: int = config.BATCH_SIZE) -> None:
        """
        Process a batch of claims and update the DataFrame

        Args:
            claims_df: DataFrame containing claims data
            unprocessed_indices: List of indices that need processing
            start_idx: Starting index in the unprocessed_indices list
            batch_size: Number of claims to process in this batch
        """
        end_idx = min(start_idx + batch_size, len(unprocessed_indices))
        batch_indices = unprocessed_indices[start_idx:end_idx]

        logger.info(f"Processing {len(batch_indices)} claims in batch")

        for idx in tqdm(batch_indices, desc="Processing claims"):
            try:
                claim_description = claims_df.loc[idx, 'claim_description']

                # Skip if already processed (double check)
                if claims_df.loc[idx, 'llm_processed'] == True:
                    continue

                # Analyze the claim
                extracted_data = self.analyze_claim(claim_description)

                if extracted_data:
                    # Update the DataFrame
                    update_dataframe_row(claims_df, idx, extracted_data)
                    logger.info(f"Successfully processed claim at index {idx}")
                else:
                    logger.warning(f"No data extracted for claim at index {idx}")

                # Small delay to avoid rate limiting
                time.sleep(0.1)

            except Exception as e:
                logger.error(f"Error processing claim at index {idx}: {e}")
                continue

def main():
    """
    Main function to run the claims analysis application
    """
    logger.info("Starting Agno LLM Claims Analysis Application")
    
    # Check API keys
    if not config.OPENAI_API_KEY and not config.ANTHROPIC_API_KEY:
        logger.error("No API keys found. Please set OPENAI_API_KEY or ANTHROPIC_API_KEY in your .env file")
        return
    
    try:
        # Load the CSV data
        df = load_csv_data()
        
        # Initialize output columns
        df = initialize_output_columns(df)
        
        # Get unprocessed claims
        unprocessed_df = get_unprocessed_claims(df)

        if len(unprocessed_df) == 0:
            logger.info("All claims have been processed!")
            return

        # Get the indices of unprocessed claims
        unprocessed_indices = unprocessed_df.index.tolist()

        # Create the claims analysis agent
        agent = ClaimsAnalysisAgent(model_name=config.DEFAULT_MODEL)

        # Process claims in batches
        total_claims = len(unprocessed_indices)
        for start_idx in range(0, total_claims, config.BATCH_SIZE):
            batch_num = start_idx // config.BATCH_SIZE + 1
            logger.info(f"Processing batch {batch_num} starting at index {start_idx}")

            # Process the batch
            agent.process_claims_batch(df, unprocessed_indices, start_idx, config.BATCH_SIZE)

            # Save progress after each batch
            save_csv_data(df)
            logger.info(f"Saved progress after processing batch {batch_num}")
        
        logger.info("Claims analysis completed successfully!")
        
    except Exception as e:
        logger.error(f"Application error: {e}")
        raise

if __name__ == "__main__":
    main()
