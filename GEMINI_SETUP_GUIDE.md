# Google Gemini API 設定指南

## 🌟 為什麼選擇 Gemini 2.5 Flash Preview 05-20？

本應用程式預設使用 Google Gemini 2.5 Flash Preview 05-20 模型，原因如下：

### 優勢
- 🚀 **超高速度**：處理速度比 GPT-4 快 2-3 倍
- 💰 **極低成本**：成本僅為 GPT-4o 的 1/20
- 🎯 **優秀準確度**：在文字分析和特徵提取方面表現卓越
- 🔄 **高穩定性**：Google 提供的企業級 API 服務
- 📊 **大容量處理**：支援長文本和批次處理

### 成本比較（處理 7510 個理賠案例）
| 模型 | 每個理賠成本 | 總成本 | 相對成本 |
|------|-------------|--------|----------|
| **Gemini 2.5 Flash Preview** | ~$0.001 | ~$8 | 1x (基準) |
| GPT-4o | ~$0.02 | ~$150 | 19x |
| Claude-3-Sonnet | ~$0.015 | ~$113 | 14x |

## 📋 取得 Google API 金鑰

### 步驟 1：建立 Google Cloud 專案
1. 前往 [Google Cloud Console](https://console.cloud.google.com/)
2. 建立新專案或選擇現有專案
3. 確保已啟用計費功能

### 步驟 2：啟用 Gemini API
1. 在 Google Cloud Console 中，前往「API 和服務」→「程式庫」
2. 搜尋「Generative AI API」或「Gemini API」
3. 點擊啟用 API

### 步驟 3：建立 API 金鑰
1. 前往「API 和服務」→「憑證」
2. 點擊「建立憑證」→「API 金鑰」
3. 複製產生的 API 金鑰
4. （建議）限制 API 金鑰的使用範圍以提高安全性

### 步驟 4：設定環境變數
```bash
# 編輯 .env 檔案
GOOGLE_API_KEY=your-actual-api-key-here
```

## 🔧 配置設定

### 預設配置（推薦）
```python
# config.py 中的預設設定
DEFAULT_MODEL = "gemini-2.5-flash-preview-05-20"
BATCH_SIZE = 10
MAX_RETRIES = 3
```

### 進階配置選項
```python
# 如果需要更高準確度，可以使用：
DEFAULT_MODEL = "gemini-pro"

# 如果需要更快速度，可以調整批次大小：
BATCH_SIZE = 20  # 增加批次大小以提高效率
```

## 🧪 測試設定

### 快速測試
```bash
# 執行演示腳本（無需 API 金鑰）
python demo.py

# 測試 API 連線
python test_single_claim.py
```

### 預期輸出
```
測試 Agno LLM 理賠分析
==================================================
使用模型: gemini-2.5-flash-preview-05-20
==================================================
✅ 所有預期欄位都成功提取！
📊 統計資訊:
   • 預期欄位數量: 18
   • 實際提取欄位數量: 18
   • 成功率: 100.0%
```

## 🚀 執行完整處理

```bash
# 處理所有理賠案例
python agno_llm_app.py
```

### 預期處理時間
- **7510 個理賠案例**
- **批次大小 10**：約 751 個批次
- **每批次約 30 秒**：總時間約 6-8 小時
- **成本**：約 $8 USD

## 🔍 監控和最佳化

### 效能監控
```python
# 在 agno_llm_app.py 中查看日誌
INFO - 正在處理批次中的 10 筆理賠案例
INFO - 成功處理索引 0 的理賠案例
INFO - 處理第 1 批次後已儲存進度
```

### 最佳化建議
1. **批次大小調整**：根據系統記憶體調整 `BATCH_SIZE`
2. **並行處理**：可以考慮多執行緒處理（需要額外開發）
3. **錯誤重試**：調整 `MAX_RETRIES` 以應對網路問題

## ⚠️ 注意事項

### API 限制
- **每分鐘請求數**：通常為 60 RPM
- **每日配額**：根據您的 Google Cloud 計費設定
- **速率限制**：應用程式已內建延遲機制

### 安全性
- 🔐 **保護 API 金鑰**：不要將 API 金鑰提交到版本控制
- 🛡️ **限制 API 範圍**：在 Google Cloud Console 中設定 API 金鑰限制
- 📊 **監控使用量**：定期檢查 Google Cloud 的使用量和費用

### 備份策略
- 💾 **定期備份**：應用程式會自動儲存進度
- 🔄 **續傳功能**：可以安全地中斷和重新啟動處理
- 📋 **結果驗證**：定期檢查提取結果的品質

## 🆘 故障排除

### 常見錯誤

#### 1. API 金鑰錯誤
```
錯誤: 找不到 API 金鑰
解決: 檢查 .env 檔案中的 GOOGLE_API_KEY 設定
```

#### 2. 配額超限
```
錯誤: Quota exceeded
解決: 等待配額重置或增加 Google Cloud 配額
```

#### 3. 網路連線問題
```
錯誤: Connection timeout
解決: 檢查網路連線，應用程式會自動重試
```

### 取得支援
- 📚 [Google AI Studio 文件](https://ai.google.dev/)
- 💬 [Google Cloud 支援](https://cloud.google.com/support)
- 🔧 [Agno Framework 文件](https://github.com/agno-agi/agno)

## 🎉 開始使用

現在您已經完成 Gemini API 的設定，可以開始使用應用程式了：

```bash
# 1. 確認設定
python demo.py

# 2. 測試單一案例
python test_single_claim.py

# 3. 執行完整處理
python agno_llm_app.py
```

享受高效、經濟的 AI 理賠分析體驗！🚀
