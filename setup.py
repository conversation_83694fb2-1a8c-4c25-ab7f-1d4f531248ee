"""
Setup script for the Agno LLM Claims Analysis Application
"""

import subprocess
import sys
import os

def install_requirements():
    """Install required packages"""
    print("Installing required packages...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        print("✅ Requirements installed successfully!")
    except subprocess.CalledProcessError as e:
        print(f"❌ Error installing requirements: {e}")
        return False
    return True

def setup_env_file():
    """Setup environment file"""
    if not os.path.exists(".env"):
        if os.path.exists(".env.example"):
            print("Creating .env file from template...")
            with open(".env.example", "r") as src, open(".env", "w") as dst:
                dst.write(src.read())
            print("✅ .env file created!")
            print("⚠️  Please edit .env file and add your API keys")
        else:
            print("❌ .env.example not found")
            return False
    else:
        print("✅ .env file already exists")
    return True

def check_data_files():
    """Check if required data files exist"""
    required_files = ["raw_data_with_word_count.csv", "lmm_extraction.json"]
    missing_files = []
    
    for file in required_files:
        if not os.path.exists(file):
            missing_files.append(file)
    
    if missing_files:
        print(f"⚠️  Missing required files: {missing_files}")
        print("Please ensure these files are in the project directory")
        return False
    else:
        print("✅ All required data files found")
        return True

def main():
    """Main setup function"""
    print("Setting up Agno LLM Claims Analysis Application")
    print("=" * 50)
    
    # Install requirements
    if not install_requirements():
        return
    
    # Setup environment file
    if not setup_env_file():
        return
    
    # Check data files
    check_data_files()
    
    print("\n" + "=" * 50)
    print("Setup completed!")
    print("\nNext steps:")
    print("1. Edit .env file and add your API keys")
    print("2. Run: python test_single_claim.py (to test)")
    print("3. Run: python agno_llm_app.py (to process all claims)")

if __name__ == "__main__":
    main()
