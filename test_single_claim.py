"""
Test script to analyze a single claim description using the Agno LLM application
"""

import json
from agno_llm_app import ClaimsAnalysisAgent
import config

def test_single_claim():
    """
    Test the claims analysis with a single example
    """
    # Example claim description from your CSV
    test_claim = "driving northbound center lane cars stopped front slammed brakes truck jack knifed right struck stationary vehicles side road earlier accident tractor heavy damage trailer slightdamage passenger side deadlined yard shop eval morning injuries reported accident per beavercreek police scene driver also ok refused treatment injuries"
    
    print("Testing Agno LLM Claims Analysis")
    print("=" * 50)
    print(f"Test claim: {test_claim}")
    print("=" * 50)
    
    try:
        # Create the agent
        agent = ClaimsAnalysisAgent(model_name=config.DEFAULT_MODEL)
        
        # Analyze the claim
        result = agent.analyze_claim(test_claim)
        
        # Display results
        print("Extracted Features:")
        print(json.dumps(result, indent=2, ensure_ascii=False))
        
        # Check if all expected fields are present
        expected_fields = config.OUTPUT_COLUMNS
        missing_fields = [field for field in expected_fields if field not in result]
        
        if missing_fields:
            print(f"\nMissing fields: {missing_fields}")
        else:
            print("\n✅ All expected fields extracted successfully!")
            
    except Exception as e:
        print(f"❌ Error during testing: {e}")

if __name__ == "__main__":
    test_single_claim()
