"""
Agno LLM 理賠分析應用程式

此應用程式使用 Agno framework 從保險理賠描述中提取關鍵特徵，
使用大型語言模型 (LLM) 處理 CSV 檔案中的理賠案例，並將提取的特徵寫回同一檔案。
"""

import time
import logging
from typing import Dict, Any
from tqdm import tqdm

from agno.agent import Agent
from agno.models.openai import OpenAIChat
from agno.models.anthropic import Claude
from agno.models.google import GoogleGemini

import config
from utils import (
    load_csv_data, 
    save_csv_data, 
    initialize_output_columns,
    parse_llm_response,
    update_dataframe_row,
    get_unprocessed_claims
)

# 設定日誌記錄
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class ClaimsAnalysisAgent:
    """
    使用 Agno framework 分析保險理賠的專門代理程式
    """

    def __init__(self, model_name: str = config.DEFAULT_MODEL):
        """
        初始化理賠分析代理程式

        Args:
            model_name: 要使用的模型名稱 (例如: "gemini-2.5-flash-preview-05-20", "gpt-4o", "claude-3-sonnet-20240229")
        """
        self.model_name = model_name
        self.agent = self._create_agent()
        
    def _create_agent(self) -> Agent:
        """
        建立並配置具有理賠分析提示詞的 Agno 代理程式

        Returns:
            已配置的 Agno 代理程式
        """
        # 從 JSON 配置載入系統提示詞
        system_prompt = self._load_system_prompt()

        # 選擇適當的模型
        if "gemini" in self.model_name.lower():
            model = GoogleGemini(id=self.model_name)
        elif "gpt" in self.model_name.lower():
            model = OpenAIChat(id=self.model_name)
        elif "claude" in self.model_name.lower():
            model = Claude(id=self.model_name)
        else:
            # 預設使用 Gemini
            model = GoogleGemini(id="gemini-2.5-flash-preview-05-20")
            
        # 建立代理程式
        agent = Agent(
            model=model,
            instructions=[
                system_prompt,
                "請僅以有效的 JSON 格式回應。",
                "請勿在 JSON 之外包含任何說明或額外文字。",
                "如果資訊不可用，請使用 null 作為值。"
            ],
            description="專門從保險理賠描述中提取關鍵特徵的專家理賠分析師",
            markdown=False
        )

        return agent
    
    def _load_system_prompt(self) -> str:
        """
        載入系統提示詞（使用固定的專業提示詞）

        Returns:
            系統提示詞字串
        """
        # 直接使用專業的系統提示詞，不再從 JSON 檔案讀取
        return self._get_professional_system_prompt()
    
    def _get_professional_system_prompt(self) -> str:
        """
        專業的系統提示詞

        Returns:
            專業系統提示詞字串
        """
        return """## Role Definition
You are an experienced "Claims Expert". Your primary responsibility is to precisely extract critical features from the `claim_description` provided by the policyholder, which are essential for making claims judgments. You will collaborate with data scientists to build an efficient claims judgment model and provide actionable insights for claims processing.


## Main Task
Identify and extract the following key information from the user-provided claim_description, and then organize them into a standardized JSON format. The total number of distinct fields in the output JSON will be 17.


## Input Format
The user will provide a single text string representing the policyholder's description of the incident.

## Output Format
Your output must be a single JSON object containing the following 17 key-value pairs. If certain information is not explicitly mentioned in the description, its value should be `null`.

**Only output the JSON structure, without providing any additional explanations, apologies, or comments, even if the description is incomplete or unclear.**


```json
{
  "accident_type": "Accident Type",
  "time_of_incident": "Time of Incident",
  "location_of_incident": "Location of Incident",
  "self_role": "Self Role",
  "self_damage_status": "Self Damage Status",
  "third_party_count": "Third Party Count",
  "third_party_details": "Third Party Details",
  "property_damage_description": "Property Damage Description",
  "personal_injury_description": "Personal Injury Description",
  "incident_narrative": "Incident Cause/Narrative",
  "potential_liability_third_party_info": "Potential Liability and Third-Party Information",
  "reporting_medical_status": "Reporting/Medical Treatment Status and Related Records",
  "environmental_factors": "Environmental Factors",
  "other_supporting_documents_mentioned": "Mention of Other Supporting Documents",
  "emotional_tone": "Emotional Tone",
  "severity_attitude": "Severity Attitude",
  "case_complexity": "Case Complexity Level",
  "case_complexity_description": "Case Complexity Description"
}
```

Extraction Details and Examples
Please extract the corresponding information from the claim_description based on the following instructions:

accident_type (Accident Type)
Content to Extract: Categorization of the nature of the incident (e.g., car accident, fall, fire, theft, medical, property damage).
Examples: "Car Accident", "Fall", "Fire", "Theft", "Medical Emergency".

time_of_incident (Time of Incident)
Content to Extract: The specific date and time the accident occurred. Extract the most precise time possible; if not precise, retain the original description.
Examples: "May 29, 2024, 3 PM", "Yesterday evening", "Last Tuesday".

location_of_incident (Location of Incident)
Content to Extract: Detailed location where the accident occurred, including streets, intersections, specific buildings, areas, etc.
Examples: "Intersection of Fuxing South Road Section 1 and Zhongxiao East Road", "My living room", "Near an escalator in a department store".

self_role (Self Role)
Content to Extract: The policyholder's role in the incident (e.g., driver, passenger, pedestrian, homeowner, injured party).
Examples: "Scooter Driver", "Pedestrian", "Homeowner".

self_damage_status (Self Damage Status)
Content to Extract: Describe whether the policyholder themselves (or their directly related property) suffered damage or injury in the incident.
Examples: "Injured and Property Damaged", "Only Property Damaged", "Only Injured", "No Apparent Damage/Injury".

third_party_count (Third Party Count)
Content to Extract: The number of individuals or entities involved in the incident other than the policyholder (e.g., 0, 1, 2, multiple).
Examples: 0, 1, 2, "Multiple".

third_party_details (Third Party Details)
Content to Extract: A brief description of the third party(s) involved (e.g., the other driver, a car, a pedestrian, a store).
Examples: "A right-turning car and its driver", "A pedestrian", "The store management".

property_damage_description (Property Damage Description)
Content to Extract: Specifically describe the damaged property, affected parts, and extent of damage. If no property damage, set to null.
Examples: "Front of scooter damaged", "Laptop screen cracked", "Roof collapsed".

personal_injury_description (Personal Injury Description)
Content to Extract: Specifically describe the injured body part(s), severity of injury, and initial treatment status. If no personal injury, set to null.
Examples: "Left calf abrasions", "Broken left ankle, hospitalized for 3 days", "Minor scratches".

incident_narrative (Incident Cause/Narrative)
Content to Extract: Briefly summarize the main process of the accident's occurrence, including the cause, development, and outcome. The original logic should be maintained as much as possible.
Examples: "Vehicle lost control due to slippery road in rain and hit the guardrail", "Right-turning car failed to yield to straight-going scooter leading to collision".

potential_liability_third_party_info (Potential Liability and Third-Party Information)
Content to Extract: Describe whether there is third-party liability, and any information regarding the third party's identity, actions, or contact details.
Examples: "Other party ran a red light", "Store floor was slippery with no warning signs", "The other driver got out to check on me and we exchanged phone numbers".

reporting_medical_status (Reporting/Medical Treatment Status and Related Records)
Content to Extract: Whether police were reported, whether medical treatment was sought, and any mentioned report numbers, hospital names, or other relevant details.
Examples: "Police reported", "Taken to hospital emergency room", "Did not seek medical attention but got an estimate from a repair shop".

environmental_factors (Environmental Factors)
Content to Extract: Environmental conditions at the time of the accident that might have influenced the event (e.g., weather, road conditions, lighting).
Examples: "Heavy rain", "Poor visibility at night", "Slippery road", "Icy road".

other_supporting_documents_mentioned (Mention of Other Supporting Documents)
Content to Extract: Any additional evidence mentioned in the policyholder's description (e.g., photos, videos, witnesses, repair estimates, medical reports).
Examples: "I took photos of the scene", "Accompanying friend can testify", "Repair shop has provided an estimate".

emotional_tone (Emotional Tone)
Content to Extract: Analyze the overall emotional tone expressed by the policyholder in their description. Focus on emotional indicators, language intensity, and mood.
Options: "Calm/Neutral", "Anxious/Worried", "Angry/Frustrated", "Sad/Distressed", "Confused/Uncertain", "Cooperative/Positive"
Examples: "Calm/Neutral", "Angry/Frustrated", "Anxious/Worried"

severity_attitude (Severity Attitude)
Content to Extract: Assess whether the policyholder appears to minimize, neutrally describe, or emphasize/exaggerate the severity of the incident.
Options: "Minimizing", "Neutral", "Emphasizing/Exaggerating"
Examples: "Neutral", "Emphasizing/Exaggerating", "Minimizing"

**case_complexity (Case Complexity Level)**
Content to Extract: Evaluate the overall complexity of the case based on multiple factors and classify it as either "Simple" or "Complex".

**Classification Criteria:**
- **Simple Cases**: Single party involvement, clear liability, minor damages, straightforward circumstances, no special populations (minors/elderly), standard processing possible
- **Complex Cases**: Multiple parties, disputed liability, significant damages, unclear circumstances, special populations involved, potential litigation risk, regulatory compliance issues

**Evaluation Factors:**
1. **Party Involvement**: Single vs. multiple parties
2. **Liability Clarity**: Clear vs. disputed responsibility
3. **Damage Severity**: Minor vs. significant property/personal injury
4. **Special Populations**: Involvement of minors, elderly, or vulnerable groups
5. **Regulatory Issues**: School supervision, workplace safety, building codes, etc.
6. **Evidence Availability**: Clear vs. insufficient evidence
7. **Third-party Complications**: Unknown parties, multiple insurers, subrogation potential

Options: "Simple", "Complex"
Examples: "Simple", "Complex"

**case_complexity_description (Case Complexity Description)**
Content to Extract: Provide a brief explanation (1-2 sentences) justifying why the case is classified as Simple or Complex. Focus on the primary factors that influenced the classification decision.

**Description Guidelines:**
- For **Simple Cases**: Highlight factors such as single party, clear fault, minor damages, adequate information, standard circumstances
- For **Complex Cases**: Identify key complexity drivers such as multiple parties, disputed liability, significant damages, special populations, missing information, regulatory concerns

**Format**: Provide a concise explanation in 1-2 sentences.
Examples:
- "Single-party workplace incident with clear causation and minor injury, requiring standard processing procedures."
- "Multiple-vehicle collision with disputed liability, significant property damage, and involvement of commercial vehicles requiring detailed investigation."
- "Incident involving a minor with potential supervision negligence claims, requiring specialized handling and regulatory compliance review."


Input Example (claim_description):
"Yesterday around 3 PM, at the intersection of Fuxing South Road Section 1 and Zhongxiao East Road, I was riding my scooter and collided with a car making a right turn. My left calf had abrasions, and the front of my scooter was also damaged. It was raining heavily at the time, and the ground was very slippery. The other driver got out to check on me and we exchanged phone numbers."

Output Example

{
  "accident_type": "Car Accident",
  "time_of_incident": "Yesterday around 3 PM",
  "location_of_incident": "Intersection of Fuxing South Road Section 1 and Zhongxiao East Road",
  "self_role": "Scooter Driver",
  "self_damage_status": "Injured and Property Damaged",
  "third_party_count": 1,
  "third_party_details": "A right-turning car and its driver",
  "property_damage_description": "Front of scooter damaged",
  "personal_injury_description": "Left calf abrasions",
  "incident_narrative": "Collided with a car making a right turn while riding a scooter",
  "potential_liability_third_party_info": "The other driver got out to check on me and we exchanged phone numbers",
  "reporting_medical_status": null,
  "environmental_factors": "Raining heavily, ground very slippery",
  "other_supporting_documents_mentioned": null,
  "emotional_tone": "Calm/Neutral",
  "severity_attitude": "Neutral",
  "case_complexity": "Simple",
  "case_complexity_description": "Single-party collision with clear circumstances and minor injuries, requiring standard processing."
}"""
    
    def analyze_claim(self, claim_description: str) -> Dict[str, Any]:
        """
        分析單一理賠描述並提取特徵

        Args:
            claim_description: 要分析的理賠描述文字

        Returns:
            包含提取特徵的字典
        """
        try:
            # 為代理程式建立提示詞
            prompt = f"理賠描述為：{claim_description}\n\n請開始提取資訊。"

            # 從代理程式取得回應
            response = self.agent.run(prompt)

            # 解析回應
            if hasattr(response, 'content'):
                response_text = response.content
            else:
                response_text = str(response)

            # 從回應中提取 JSON
            extracted_data = parse_llm_response(response_text)

            return extracted_data

        except Exception as e:
            logger.error(f"分析理賠時發生錯誤: {e}")
            return {}
    
    def process_claims_batch(self, claims_df, unprocessed_indices, start_idx: int = 0, batch_size: int = config.BATCH_SIZE) -> None:
        """
        處理一批理賠案例並更新 DataFrame

        Args:
            claims_df: 包含理賠資料的 DataFrame
            unprocessed_indices: 需要處理的索引清單
            start_idx: 在 unprocessed_indices 清單中的起始索引
            batch_size: 此批次要處理的理賠案例數量
        """
        end_idx = min(start_idx + batch_size, len(unprocessed_indices))
        batch_indices = unprocessed_indices[start_idx:end_idx]

        logger.info(f"正在處理批次中的 {len(batch_indices)} 筆理賠案例")

        for idx in tqdm(batch_indices, desc="處理理賠案例"):
            try:
                claim_description = claims_df.loc[idx, 'claim_description']

                # 如果已處理則跳過（雙重檢查）
                if claims_df.loc[idx, 'llm_processed'] == True:
                    continue

                # 分析理賠案例
                extracted_data = self.analyze_claim(claim_description)

                if extracted_data:
                    # 更新 DataFrame
                    update_dataframe_row(claims_df, idx, extracted_data)
                    logger.info(f"成功處理索引 {idx} 的理賠案例")
                else:
                    logger.warning(f"索引 {idx} 的理賠案例未提取到資料")

                # 小延遲以避免速率限制
                time.sleep(0.1)

            except Exception as e:
                logger.error(f"處理索引 {idx} 的理賠案例時發生錯誤: {e}")
                continue

def main():
    """
    執行理賠分析應用程式的主函數
    """
    logger.info("啟動 Agno LLM 理賠分析應用程式")

    # 檢查 API 金鑰
    if not config.OPENAI_API_KEY and not config.ANTHROPIC_API_KEY and not config.GOOGLE_API_KEY:
        logger.error("找不到 API 金鑰。請在 .env 檔案中設定 OPENAI_API_KEY、ANTHROPIC_API_KEY 或 GOOGLE_API_KEY")
        return
    
    try:
        # 載入 CSV 資料
        df = load_csv_data()

        # 初始化輸出欄位
        df = initialize_output_columns(df)

        # 取得未處理的理賠案例
        unprocessed_df = get_unprocessed_claims(df)

        if len(unprocessed_df) == 0:
            logger.info("所有理賠案例都已處理完成！")
            return

        # 取得未處理理賠案例的索引
        unprocessed_indices = unprocessed_df.index.tolist()

        # 建立理賠分析代理程式
        agent = ClaimsAnalysisAgent(model_name=config.DEFAULT_MODEL)

        # 批次處理理賠案例
        total_claims = len(unprocessed_indices)
        for start_idx in range(0, total_claims, config.BATCH_SIZE):
            batch_num = start_idx // config.BATCH_SIZE + 1
            logger.info(f"處理第 {batch_num} 批次，從索引 {start_idx} 開始")

            # 處理批次
            agent.process_claims_batch(df, unprocessed_indices, start_idx, config.BATCH_SIZE)

            # 每批次後儲存進度
            save_csv_data(df)
            logger.info(f"處理第 {batch_num} 批次後已儲存進度")

        logger.info("理賠分析成功完成！")

    except Exception as e:
        logger.error(f"應用程式錯誤: {e}")
        raise

if __name__ == "__main__":
    main()
