"""
Utility functions for the Agno LLM Claims Analysis Application
"""
import pandas as pd
import json
import logging
from typing import Dict, Any, Optional
from config import CSV_FILE_PATH, OUTPUT_COLUMNS

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def load_csv_data(file_path: str = CSV_FILE_PATH) -> pd.DataFrame:
    """
    Load the CSV data file
    
    Args:
        file_path: Path to the CSV file
        
    Returns:
        DataFrame containing the CSV data
    """
    try:
        df = pd.read_csv(file_path)
        logger.info(f"Successfully loaded CSV with {len(df)} rows")
        return df
    except Exception as e:
        logger.error(f"Error loading CSV file: {e}")
        raise

def save_csv_data(df: pd.DataFrame, file_path: str = CSV_FILE_PATH) -> None:
    """
    Save the DataFrame back to CSV
    
    Args:
        df: DataFrame to save
        file_path: Path to save the CSV file
    """
    try:
        df.to_csv(file_path, index=False)
        logger.info(f"Successfully saved CSV with {len(df)} rows")
    except Exception as e:
        logger.error(f"Error saving CSV file: {e}")
        raise

def initialize_output_columns(df: pd.DataFrame) -> pd.DataFrame:
    """
    Initialize output columns in the DataFrame if they don't exist
    
    Args:
        df: Input DataFrame
        
    Returns:
        DataFrame with initialized output columns
    """
    for column in OUTPUT_COLUMNS:
        if column not in df.columns:
            df[column] = None
    
    # Add a processing status column
    if 'llm_processed' not in df.columns:
        df['llm_processed'] = False
        
    return df

def parse_llm_response(response_text: str) -> Dict[str, Any]:
    """
    Parse the LLM response and extract the JSON data
    
    Args:
        response_text: Raw response text from LLM
        
    Returns:
        Dictionary containing extracted features
    """
    try:
        # Try to find JSON in the response
        start_idx = response_text.find('{')
        end_idx = response_text.rfind('}') + 1
        
        if start_idx != -1 and end_idx != -1:
            json_str = response_text[start_idx:end_idx]
            parsed_data = json.loads(json_str)
            return parsed_data
        else:
            logger.warning("No JSON found in response")
            return {}
            
    except json.JSONDecodeError as e:
        logger.error(f"Error parsing JSON response: {e}")
        return {}
    except Exception as e:
        logger.error(f"Unexpected error parsing response: {e}")
        return {}

def update_dataframe_row(df: pd.DataFrame, index: int, extracted_data: Dict[str, Any]) -> pd.DataFrame:
    """
    Update a specific row in the DataFrame with extracted data
    
    Args:
        df: DataFrame to update
        index: Row index to update
        extracted_data: Dictionary containing extracted features
        
    Returns:
        Updated DataFrame
    """
    try:
        # Update each column with extracted data
        for column in OUTPUT_COLUMNS:
            if column in extracted_data:
                df.at[index, column] = extracted_data[column]
        
        # Mark as processed
        df.at[index, 'llm_processed'] = True
        
        return df
    except Exception as e:
        logger.error(f"Error updating DataFrame row {index}: {e}")
        return df

def get_unprocessed_claims(df: pd.DataFrame) -> pd.DataFrame:
    """
    Get claims that haven't been processed yet
    
    Args:
        df: Input DataFrame
        
    Returns:
        DataFrame containing only unprocessed claims
    """
    if 'llm_processed' not in df.columns:
        return df
    
    unprocessed = df[df['llm_processed'] != True]
    logger.info(f"Found {len(unprocessed)} unprocessed claims")
    return unprocessed
