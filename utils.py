"""
Agno LLM 理賠分析應用程式工具函數
"""
import pandas as pd
import json
import logging
from typing import Dict, Any
from config import CSV_FILE_PATH, OUTPUT_COLUMNS

# 設定日誌記錄
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def load_csv_data(file_path: str = CSV_FILE_PATH) -> pd.DataFrame:
    """
    載入 CSV 資料檔案

    Args:
        file_path: CSV 檔案路徑

    Returns:
        包含 CSV 資料的 DataFrame
    """
    try:
        df = pd.read_csv(file_path)
        logger.info(f"成功載入 CSV 檔案，共 {len(df)} 筆資料")
        return df
    except Exception as e:
        logger.error(f"載入 CSV 檔案時發生錯誤: {e}")
        raise

def save_csv_data(df: pd.DataFrame, file_path: str = CSV_FILE_PATH) -> None:
    """
    將 DataFrame 儲存回 CSV 檔案

    Args:
        df: 要儲存的 DataFrame
        file_path: 儲存 CSV 檔案的路徑
    """
    try:
        df.to_csv(file_path, index=False)
        logger.info(f"成功儲存 CSV 檔案，共 {len(df)} 筆資料")
    except Exception as e:
        logger.error(f"儲存 CSV 檔案時發生錯誤: {e}")
        raise

def initialize_output_columns(df: pd.DataFrame) -> pd.DataFrame:
    """
    初始化 DataFrame 中的輸出欄位（如果不存在的話）

    Args:
        df: 輸入的 DataFrame

    Returns:
        已初始化輸出欄位的 DataFrame
    """
    for column in OUTPUT_COLUMNS:
        if column not in df.columns:
            df[column] = None

    # 新增處理狀態欄位
    if 'llm_processed' not in df.columns:
        df['llm_processed'] = False

    return df

def parse_llm_response(response_text: str) -> Dict[str, Any]:
    """
    解析 LLM 回應並提取 JSON 資料

    Args:
        response_text: LLM 的原始回應文字

    Returns:
        包含提取特徵的字典
    """
    try:
        # 嘗試在回應中找到 JSON
        start_idx = response_text.find('{')
        end_idx = response_text.rfind('}') + 1

        if start_idx != -1 and end_idx != -1:
            json_str = response_text[start_idx:end_idx]
            parsed_data = json.loads(json_str)
            return parsed_data
        else:
            logger.warning("在回應中找不到 JSON 格式")
            return {}

    except json.JSONDecodeError as e:
        logger.error(f"解析 JSON 回應時發生錯誤: {e}")
        return {}
    except Exception as e:
        logger.error(f"解析回應時發生未預期的錯誤: {e}")
        return {}

def update_dataframe_row(df: pd.DataFrame, index: int, extracted_data: Dict[str, Any]) -> pd.DataFrame:
    """
    使用提取的資料更新 DataFrame 中的特定列

    Args:
        df: 要更新的 DataFrame
        index: 要更新的列索引
        extracted_data: 包含提取特徵的字典

    Returns:
        更新後的 DataFrame
    """
    try:
        # 使用提取的資料更新每個欄位
        for column in OUTPUT_COLUMNS:
            if column in extracted_data:
                df.at[index, column] = extracted_data[column]

        # 標記為已處理
        df.at[index, 'llm_processed'] = True

        return df
    except Exception as e:
        logger.error(f"更新 DataFrame 第 {index} 列時發生錯誤: {e}")
        return df

def get_unprocessed_claims(df: pd.DataFrame) -> pd.DataFrame:
    """
    取得尚未處理的理賠案例

    Args:
        df: 輸入的 DataFrame

    Returns:
        僅包含未處理理賠案例的 DataFrame
    """
    if 'llm_processed' not in df.columns:
        return df

    unprocessed = df[df['llm_processed'] != True]
    logger.info(f"找到 {len(unprocessed)} 筆未處理的理賠案例")
    return unprocessed
