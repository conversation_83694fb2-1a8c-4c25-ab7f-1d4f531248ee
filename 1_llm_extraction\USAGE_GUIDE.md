# Agno LLM 理賠分析 - 使用指南

## 快速開始

### 1. 安裝和設定

```bash
# 1. 安裝依賴套件
pip install -r requirements.txt

# 2. 設定環境變數
cp .env.example .env
# 編輯 .env 檔案，添加您的 Google API 金鑰（推薦）

# 3. 執行設定腳本（可選）
python setup.py
```

### 🌟 推薦使用 Google Gemini 2.5 Flash Preview 05-20

本應用程式預設使用 Google Gemini 2.5 Flash Preview 05-20 模型，具有以下優勢：
- 🚀 **高效能**：處理速度快，成本效益高
- 🎯 **高準確度**：在文字分析和特徵提取方面表現優異
- 💰 **經濟實惠**：相較於其他模型具有更好的性價比
- 🔄 **穩定性**：Google 提供的穩定 API 服務

### 2. 測試應用程式

```bash
# 運行演示腳本（不需要 API 金鑰）
python demo.py

# 測試單一理賠分析（需要 API 金鑰）
python test_single_claim.py
```

### 3. 批次處理所有理賠

```bash
# 處理所有理賠數據
python agno_llm_app.py
```

## 詳細說明

### 應用程式架構

```
agno_llm_app.py     # 主要應用程式
├── ClaimsAnalysisAgent  # Agno LLM Agent 類別
├── config.py            # 配置設定
├── utils.py             # 工具函數
└── demo.py              # 演示腳本
```

### 數據流程

1. **輸入**: `raw_data_with_word_count.csv` 中的 `claim_description` 欄位
2. **處理**: 使用 Agno framework 和 LLM 提取 17 個特徵欄位
3. **輸出**: 將提取的特徵添加到原始 CSV 文件中

### 提取的特徵欄位

| 欄位名稱 | 描述 | 範例 |
|---------|------|------|
| accident_type | 事故類型 | "Vehicle Accident" |
| time_of_incident | 事故時間 | "morning" |
| location_of_incident | 事故地點 | "northbound center lane" |
| self_role | 投保人角色 | "Driver" |
| self_damage_status | 損害狀態 | "Property Damaged" |
| third_party_count | 第三方數量 | "Multiple" |
| third_party_details | 第三方詳情 | "Stationary vehicles" |
| property_damage_description | 財產損害 | "Heavy damage to tractor" |
| personal_injury_description | 人身傷害 | "Driver refused treatment" |
| incident_narrative | 事故經過 | "Driver slammed brakes..." |
| potential_liability_third_party_info | 責任資訊 | "Police on scene" |
| reporting_medical_status | 報案狀態 | "Police reported" |
| environmental_factors | 環境因素 | null |
| other_supporting_documents_mentioned | 支持文件 | "Police report" |
| emotional_tone | 情緒語調 | "Neutral" |
| severity_attitude | 嚴重性態度 | "Neutral" |
| case_complexity | 案件複雜度 | "Complex" |
| case_complexity_description | 複雜度說明 | "Multiple vehicle collision..." |

### 配置選項

在 `config.py` 中可以調整：

```python
# 模型選擇
DEFAULT_MODEL = "gpt-4o"  # 或 "claude-3-sonnet-20240229"

# 批次大小
BATCH_SIZE = 10  # 每批處理的理賠數量

# 重試次數
MAX_RETRIES = 3  # API 調用失敗時的重試次數
```

### 支援的 LLM 模型

#### Google Gemini 模型（推薦）
- `gemini-2.5-flash-preview-05-20` (預設，推薦)
- `gemini-pro`

#### OpenAI 模型
- `gpt-4o`
- `gpt-4-turbo`
- `gpt-3.5-turbo`

#### Anthropic 模型
- `claude-3-sonnet-20240229`
- `claude-3-haiku-20240307`

### 錯誤處理

應用程式包含完整的錯誤處理機制：

1. **API 錯誤**: 自動重試機制
2. **JSON 解析錯誤**: 跳過並記錄錯誤
3. **文件錯誤**: 詳細錯誤訊息
4. **網路錯誤**: 延遲重試

### 性能優化

1. **批次處理**: 避免記憶體溢出
2. **速率限制**: 防止 API 限制
3. **斷點續傳**: 跳過已處理的理賠
4. **進度保存**: 每批次後自動保存

### 監控和日誌

應用程式提供詳細的日誌記錄：

```
INFO - Successfully loaded CSV with 7510 rows
INFO - Found 7510 unprocessed claims
INFO - Processing batch 1 starting at index 0
INFO - Successfully processed claim at index 0
INFO - Saved progress after processing batch 1
```

### 成本估算

#### Google Gemini 2.5 Flash Preview 05-20（推薦）
- 輸入：每 1M tokens 約 $0.075
- 輸出：每 1M tokens 約 $0.30
- 平均每個理賠描述：~100 tokens
- 系統提示：~3000 tokens
- 預估每個理賠成本：~$0.001
- 7510 個理賠總成本：~$8

#### 其他模型參考
- **GPT-4o**：每個理賠約 $0.02，總成本約 $150
- **Claude-3-Sonnet**：每個理賠約 $0.015，總成本約 $113

### 故障排除

#### 常見問題

1. **ModuleNotFoundError**
   ```bash
   pip install -r requirements.txt
   ```

2. **API 金鑰錯誤**
   ```bash
   # 檢查 .env 文件
   cat .env
   ```

3. **記憶體不足**
   ```python
   # 在 config.py 中減少批次大小
   BATCH_SIZE = 5
   ```

4. **速率限制**
   ```python
   # 增加延遲時間
   time.sleep(1.0)  # 在 agno_llm_app.py 中
   ```

### 最佳實踐

1. **備份數據**: 處理前備份原始 CSV
2. **測試先行**: 先用小批次測試
3. **監控成本**: 定期檢查 API 使用量
4. **錯誤檢查**: 定期檢查處理結果的品質

## 技術支援

如果遇到問題，請檢查：

1. 日誌文件中的錯誤訊息
2. API 金鑰是否正確設定
3. 網路連接是否正常
4. 依賴套件是否正確安裝

## 更新和維護

定期更新依賴套件：

```bash
pip install --upgrade agno openai anthropic pandas
```
