{"name": "理賠分析 - 文本特徵提取", "nodes": [{"parameters": {}, "type": "n8n-nodes-base.manualTrigger", "typeVersion": 1, "position": [-600, -80], "id": "805fac9f-836a-4181-a0f0-99da23b62011", "name": "When clicking ‘Test workflow’"}, {"parameters": {"documentId": {"__rl": true, "value": "1D0g8DcpX4exgv5LnAQEWiTwpEa-M4wyrIOZILvE8ENk", "mode": "list", "cachedResultName": "理賠分析專案", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1D0g8DcpX4exgv5LnAQEWiTwpEa-M4wyrIOZILvE8ENk/edit?usp=drivesdk"}, "sheetName": {"__rl": true, "value": *********, "mode": "list", "cachedResultName": "raw_data", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1D0g8DcpX4exgv5LnAQEWiTwpEa-M4wyrIOZILvE8ENk/edit#gid=*********"}, "filtersUI": {"values": [{"lookupColumn": "llm_extract", "lookupValue": "N"}]}, "options": {}}, "type": "n8n-nodes-base.googleSheets", "typeVersion": 4.5, "position": [-400, -80], "id": "a292c666-fb2c-4b8c-a9ff-aad8d9a05e01", "name": "Google Sheets", "credentials": {"googleSheetsOAuth2Api": {"id": "6m4TJIgG5mP2JOZi", "name": "Google Sheets account"}}}, {"parameters": {"assignments": {"assignments": [{"id": "b1d997cc-7bf0-486a-a81a-9b6ee7be0f5d", "name": "row_number", "value": "={{ $json.row_number }}", "type": "number"}, {"id": "91146eaf-e6ce-4d7a-892d-6cd1f6d257ed", "name": "claim_description", "value": "={{ $json.claim_description }}", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [20, -80], "id": "ee99e864-d205-4179-b5c8-d2d8ebcb7196", "name": "<PERSON>"}, {"parameters": {"model": "grok-3", "options": {"temperature": 0.2}}, "type": "@n8n/n8n-nodes-langchain.lmChatXAiGrok", "typeVersion": 1, "position": [-340, 280], "id": "a690b7c9-cf9f-4444-a94c-707bd95dd5a5", "name": "xAI Grok Chat Model", "credentials": {"xAiApi": {"id": "Qa3ybyMWIzfXqA9B", "name": "xAi account"}}}, {"parameters": {"promptType": "define", "text": "=The 'claims_description' is {{ $json.claim_description }}\n\nplease start to extract information.\n", "options": {"systemMessage": "=## Role Definition\nYou are an experienced \"Claims Expert\". Your primary responsibility is to precisely extract critical features from the `claim_description` provided by the policyholder, which are essential for making claims judgments. You will collaborate with data scientists to build an efficient claims judgment model and provide actionable insights for claims processing.\n\n\n## Main Task\nIdentify and extract the following key information from the user-provided claim_description, and then organize them into a standardized JSON format. The total number of distinct fields in the output JSON will be 17.\n\n\n## Input Format\nThe user will provide a single text string representing the policyholder's description of the incident. \n\n## Output Format\nYour output must be a single JSON object containing the following 17 key-value pairs. If certain information is not explicitly mentioned in the description, its value should be `null`.\n\n**Only output the JSON structure, without providing any additional explanations, apologies, or comments, even if the description is incomplete or unclear.**\n\n\n```json\n{\n  \"accident_type\": \"Accident Type\",\n  \"time_of_incident\": \"Time of Incident\",\n  \"location_of_incident\": \"Location of Incident\",\n  \"self_role\": \"Self Role\",\n  \"self_damage_status\": \"Self Damage Status\",\n  \"third_party_count\": \"Third Party Count\",\n  \"third_party_details\": \"Third Party Details\",\n  \"property_damage_description\": \"Property Damage Description\",\n  \"personal_injury_description\": \"Personal Injury Description\",\n  \"incident_narrative\": \"Incident Cause/Narrative\",\n  \"potential_liability_third_party_info\": \"Potential Liability and Third-Party Information\",\n  \"reporting_medical_status\": \"Reporting/Medical Treatment Status and Related Records\",\n  \"environmental_factors\": \"Environmental Factors\",\n  \"other_supporting_documents_mentioned\": \"Mention of Other Supporting Documents\",\n  \"emotional_tone\": \"Emotional Tone\",\n  \"severity_attitude\": \"Severity Attitude\",\n  \"case_complexity\": \"Case Complexity Level\",\n  \"case_complexity_description\": \"Case Complexity Description\"\n}\n```\n\nExtraction Details and Examples\nPlease extract the corresponding information from the claim_description based on the following instructions:\n\naccident_type (Accident Type)\nContent to Extract: Categorization of the nature of the incident (e.g., car accident, fall, fire, theft, medical, property damage).\nExamples: \"Car Accident\", \"Fall\", \"Fire\", \"Theft\", \"Medical Emergency\".\n\ntime_of_incident (Time of Incident)\nContent to Extract: The specific date and time the accident occurred. Extract the most precise time possible; if not precise, retain the original description.\nExamples: \"May 29, 2024, 3 PM\", \"Yesterday evening\", \"Last Tuesday\".\n\nlocation_of_incident (Location of Incident)\nContent to Extract: Detailed location where the accident occurred, including streets, intersections, specific buildings, areas, etc.\nExamples: \"Intersection of Fuxing South Road Section 1 and Zhongxiao East Road\", \"My living room\", \"Near an escalator in a department store\".\n\nself_role (Self Role)\nContent to Extract: The policyholder's role in the incident (e.g., driver, passenger, pedestrian, homeowner, injured party).\nExamples: \"Scooter Driver\", \"Pedestrian\", \"Homeowner\".\n\nself_damage_status (Self Damage Status)\nContent to Extract: Describe whether the policyholder themselves (or their directly related property) suffered damage or injury in the incident.\nExamples: \"Injured and Property Damaged\", \"Only Property Damaged\", \"Only Injured\", \"No Apparent Damage/Injury\".\nthird_party_count (Third Party Count)\nContent to Extract: The number of individuals or entities involved in the incident other than the policyholder (e.g., 0, 1, 2, multiple).\nExamples: 0, 1, 2, \"Multiple\".\n\nthird_party_details (Third Party Details)\nContent to Extract: A brief description of the third party(s) involved (e.g., the other driver, a car, a pedestrian, a store).\nExamples: \"A right-turning car and its driver\", \"A pedestrian\", \"The store management\".\nproperty_damage_description (Property Damage Description)\nContent to Extract: Specifically describe the damaged property, affected parts, and extent of damage. If no property damage, set to null.\nExamples: \"Front of scooter damaged\", \"Laptop screen cracked\", \"Roof collapsed\".\n\npersonal_injury_description (Personal Injury Description)\nContent to Extract: Specifically describe the injured body part(s), severity of injury, and initial treatment status. If no personal injury, set to null.\nExamples: \"Left calf abrasions\", \"Broken left ankle, hospitalized for 3 days\", \"Minor scratches\".\n\nincident_narrative (Incident Cause/Narrative)\nContent to Extract: Briefly summarize the main process of the accident's occurrence, including the cause, development, and outcome. The original logic should be maintained as much as possible.\nExamples: \"Vehicle lost control due to slippery road in rain and hit the guardrail\", \"Right-turning car failed to yield to straight-going scooter leading to collision\".\n\npotential_liability_third_party_info (Potential Liability and Third-Party Information)\nContent to Extract: Describe whether there is third-party liability, and any information regarding the third party's identity, actions, or contact details.\nExamples: \"Other party ran a red light\", \"Store floor was slippery with no warning signs\", \"The other driver got out to check on me and we exchanged phone numbers\".\n\nreporting_medical_status (Reporting/Medical Treatment Status and Related Records)\nContent to Extract: Whether police were reported, whether medical treatment was sought, and any mentioned report numbers, hospital names, or other relevant details.\nExamples: \"Police reported\", \"Taken to hospital emergency room\", \"Did not seek medical attention but got an estimate from a repair shop\".\n\nenvironmental_factors (Environmental Factors)\nContent to Extract: Environmental conditions at the time of the accident that might have influenced the event (e.g., weather, road conditions, lighting).\nExamples: \"Heavy rain\", \"Poor visibility at night\", \"Slippery road\", \"Icy road\".\n\nother_supporting_documents_mentioned (Mention of Other Supporting Documents)\nContent to Extract: Any additional evidence mentioned in the policyholder's description (e.g., photos, videos, witnesses, repair estimates, medical reports).\nExamples: \"I took photos of the scene\", \"Accompanying friend can testify\", \"Repair shop has provided an estimate\".\n\nemotional_tone (Emotional Tone)\nContent to Extract: Analyze the overall emotional tone expressed by the policyholder in their description. Focus on emotional indicators, language intensity, and mood.\nOptions: \"Calm/Neutral\", \"Anxious/Worried\", \"Angry/Frustrated\", \"Sad/Distressed\", \"Confused/Uncertain\", \"Cooperative/Positive\"\nExamples: \"Calm/Neutral\", \"Angry/Frustrated\", \"Anxious/Worried\"\n\nseverity_attitude (Severity Attitude)\nContent to Extract: Assess whether the policyholder appears to minimize, neutrally describe, or emphasize/exaggerate the severity of the incident.\nOptions: \"Minimizing\", \"Neutral\", \"Emphasizing/Exaggerating\"\nExamples: \"Neutral\", \"Emphasizing/Exaggerating\", \"Minimizing\"\n\n**case_complexity (Case Complexity Level)**\nContent to Extract: Evaluate the overall complexity of the case based on multiple factors and classify it as either \"Simple\" or \"Complex\".\n\n\n**Classification Criteria:**\n- **Simple Cases**: Single party involvement, clear liability, minor damages, straightforward circumstances, no special populations (minors/elderly), standard processing possible\n- **Complex Cases**: Multiple parties, disputed liability, significant damages, unclear circumstances, special populations involved, potential litigation risk, regulatory compliance issues\n\n**Evaluation Factors:**\n1. **Party Involvement**: Single vs. multiple parties\n2. **Liability Clarity**: Clear vs. disputed responsibility\n3. **Damage Severity**: Minor vs. significant property/personal injury\n4. **Special Populations**: Involvement of minors, elderly, or vulnerable groups\n5. **Regulatory Issues**: School supervision, workplace safety, building codes, etc.\n6. **Evidence Availability**: Clear vs. insufficient evidence\n7. **Third-party Complications**: Unknown parties, multiple insurers, subrogation potential\n\nOptions: \"Simple\", \"Complex\"\nExamples: \"Simple\", \"Complex\"\n\n**case_complexity_description (Case Complexity Description)**\nContent to Extract: Provide a brief explanation (1-2 sentences) justifying why the case is classified as Simple or Complex. Focus on the primary factors that influenced the classification decision.\n\n  **Description Guidelines:**\n  - For **Simple Cases**: Highlight factors such as single party, clear fault, minor damages, adequate information, standard circumstances\n  - For **Complex Cases**: Identify key complexity drivers such as multiple parties, disputed liability, significant damages, special populations, missing information, regulatory concerns\n  \n  **Format**: Provide a concise explanation in 1-2 sentences.\n  Examples: \n  - \"Single-party workplace incident with clear causation and minor injury, requiring standard processing procedures.\"\n  - \"Multiple-vehicle collision with disputed liability, significant property damage, and involvement of commercial vehicles requiring detailed investigation.\"\n  - \"Incident involving a minor with potential supervision negligence claims, requiring specialized handling and regulatory compliance review.\"\n\n\nInput Example (claim_description):\n\"Yesterday around 3 PM, at the intersection of Fuxing South Road Section 1 and Zhongxiao East Road, I was riding my scooter and collided with a car making a right turn. My left calf had abrasions, and the front of my scooter was also damaged. It was raining heavily at the time, and the ground was very slippery. The other driver got out to check on me and we exchanged phone numbers.\"\n\nOutput Example\n\n{\n  \"accident_type\": \"Car Accident\",\n  \"time_of_incident\": \"Yesterday around 3 PM\",\n  \"location_of_incident\": \"Intersection of Fuxing South Road Section 1 and Zhongxiao East Road\",\n  \"self_role\": \"Scooter Driver\",\n  \"self_damage_status\": \"Injured and Property Damaged\",\n  \"third_party_count\": 1,\n  \"third_party_details\": \"A right-turning car and its driver\",\n  \"property_damage_description\": \"Front of scooter damaged\",\n  \"personal_injury_description\": \"Left calf abrasions\",\n  \"incident_narrative\": \"Collided with a car making a right turn while riding a scooter\",\n  \"potential_liability_third_party_info\": \"The other driver got out to check on me and we exchanged phone numbers\",\n  \"reporting_medical_status\": null,\n  \"environmental_factors\": \"Raining heavily, ground very slippery\",\n  \"other_supporting_documents_mentioned\": null\n}"}}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1.9, "position": [200, -80], "id": "20b8c44d-9e6c-4673-abd7-c43ea6d56019", "name": "Claims Feature Extraction Expert"}, {"parameters": {"jsCode": "// 取得 AI Agent的輸出\nconst rawInput = $input.first().json;\n\n// 提取 \"output\" 欄位中的字串\nlet jsonString = rawInput.output;\n\nif (!jsonString) {\n  throw new Error(\"json parser1 輸出中無法找到 'output' 欄位\");\n}\n\n// 清理並轉義 JSON 字串\njsonString = jsonString\n  .replace(/```json\\n?/, '')\n  .replace(/\\n?```/, '')\n  .replace(/\\\\/g, '\\\\\\\\')  // 轉義反斜線\n  .replace(/\"/g, '\\\\\"')    // 轉義雙引號\n  .replace(/\\n/g, '\\\\n')   // 將換行符轉為 \\n\n  .trim();\n\n// 嘗試解析 JSON\ntry {\n  const parsedJson = JSON.parse(`{\"temp\": \"${jsonString}\"}`).temp; // 先包裝成合法 JSON\n  return [{ json: JSON.parse(parsedJson) }]; // 二次解析原始內容\n} catch (error) {\n  throw new Error(`JSON 解析失敗: ${error.message}\\n原始字串: ${jsonString}`);\n}"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [540, -80], "id": "102fbd3a-3802-468b-bba2-1a22bece5615", "name": "json parser2"}, {"parameters": {"operation": "update", "documentId": {"__rl": true, "value": "1D0g8DcpX4exgv5LnAQEWiTwpEa-M4wyrIOZILvE8ENk", "mode": "list", "cachedResultName": "理賠分析專案", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1D0g8DcpX4exgv5LnAQEWiTwpEa-M4wyrIOZILvE8ENk/edit?usp=drivesdk"}, "sheetName": {"__rl": true, "value": *********, "mode": "list", "cachedResultName": "raw_data", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1D0g8DcpX4exgv5LnAQEWiTwpEa-M4wyrIOZILvE8ENk/edit#gid=*********"}, "columns": {"mappingMode": "defineBelow", "value": {"row_number": "={{ $('Edit Fields').item.json.row_number }}", "llm_extract": "Y", "accident_type": "={{ $json.accident_type }}", "time_of_incident": "={{ $json.time_of_incident }}", "location_of_incident": "={{ $json.location_of_incident }}", "incident_narrative": "={{ $json.incident_narrative }}", "potential_liability_third_party_info": "={{ $json.potential_liability_third_party_info }}", "reporting_medical_status": "={{ $json.potential_liability_third_party_info }}", "environmental_factors": "={{ $json.environmental_factors }}", "other_supporting_documents_mentioned": "={{ $json.other_supporting_documents_mentioned }}", "self_role": "={{ $json.self_role }}", "self_damage_status": "={{ $json.self_damage_status }}", "third_party_count": "={{ $json.third_party_count }}", "third_party_details": "={{ $json.third_party_details }}", "property_damage_description": "={{ $json.property_damage_description }}", "personal_injury_description": "={{ $json.personal_injury_description }}", "emotional_tone": "={{ $json.emotional_tone }}", "severity_attitude": "={{ $json.severity_attitude }}", "case_complexity": "={{ $json.case_complexity }}", "case_complexity_description": "={{ $json.case_complexity_description }}"}, "matchingColumns": ["row_number"], "schema": [{"id": "claim_description", "displayName": "claim_description", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": true}, {"id": "coverage_code", "displayName": "coverage_code", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": true}, {"id": "accident_source", "displayName": "accident_source", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": true}, {"id": "coverage_code_encoded", "displayName": "coverage_code_encoded", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": true}, {"id": "accident_source_encoded", "displayName": "accident_source_encoded", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": true}, {"id": "coverage_bucket", "displayName": "coverage_bucket", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": true}, {"id": "accident_bucket", "displayName": "accident_bucket", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": true}, {"id": "word_count", "displayName": "word_count", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": true}, {"id": "llm_extract", "displayName": "llm_extract", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "accident_type", "displayName": "accident_type", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "time_of_incident", "displayName": "time_of_incident", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "location_of_incident", "displayName": "location_of_incident", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "self_role", "displayName": "self_role", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "self_damage_status", "displayName": "self_damage_status", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "third_party_count", "displayName": "third_party_count", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "third_party_details", "displayName": "third_party_details", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "property_damage_description", "displayName": "property_damage_description", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "personal_injury_description", "displayName": "personal_injury_description", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "incident_narrative", "displayName": "incident_narrative", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "potential_liability_third_party_info", "displayName": "potential_liability_third_party_info", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "reporting_medical_status", "displayName": "reporting_medical_status", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "environmental_factors", "displayName": "environmental_factors", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "other_supporting_documents_mentioned", "displayName": "other_supporting_documents_mentioned", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "emotional_tone", "displayName": "emotional_tone", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "severity_attitude", "displayName": "severity_attitude", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "case_complexity", "displayName": "case_complexity", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "case_complexity_description", "displayName": "case_complexity_description", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "row_number", "displayName": "row_number", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "readOnly": true, "removed": false}], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}}, "type": "n8n-nodes-base.googleSheets", "typeVersion": 4.5, "position": [740, -80], "id": "107ca8b8-7d93-4fb4-9078-441ed697f6be", "name": "Google Sheets1", "retryOnFail": true, "credentials": {"googleSheetsOAuth2Api": {"id": "6m4TJIgG5mP2JOZi", "name": "Google Sheets account"}}}, {"parameters": {"options": {}}, "type": "n8n-nodes-base.splitInBatches", "typeVersion": 3, "position": [-180, -80], "id": "2d78cffd-f0f7-4857-a885-a54a53ea638c", "name": "Loop Over Items"}, {"parameters": {"modelName": "models/gemini-2.5-flash-preview-05-20", "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatGoogleGemini", "typeVersion": 1, "position": [80, 140], "id": "20f78643-ecb6-4877-8145-c8ab69fe34b4", "name": "Google Gemini Chat Model", "credentials": {"googlePalmApi": {"id": "4YIucmDWDItlxG2A", "name": "Google Gemini - mom"}}}], "pinData": {}, "connections": {"When clicking ‘Test workflow’": {"main": [[{"node": "Google Sheets", "type": "main", "index": 0}]]}, "Google Sheets": {"main": [[{"node": "Loop Over Items", "type": "main", "index": 0}]]}, "Edit Fields": {"main": [[{"node": "Claims Feature Extraction Expert", "type": "main", "index": 0}]]}, "xAI Grok Chat Model": {"ai_languageModel": [[]]}, "Claims Feature Extraction Expert": {"main": [[{"node": "json parser2", "type": "main", "index": 0}]]}, "json parser2": {"main": [[{"node": "Google Sheets1", "type": "main", "index": 0}]]}, "Loop Over Items": {"main": [[], [{"node": "<PERSON>", "type": "main", "index": 0}]]}, "Google Sheets1": {"main": [[{"node": "Loop Over Items", "type": "main", "index": 0}]]}, "Google Gemini Chat Model": {"ai_languageModel": [[{"node": "Claims Feature Extraction Expert", "type": "ai_languageModel", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "445e43d4-39b1-4854-8fd3-d14cb030c4a1", "meta": {"templateCredsSetupCompleted": true, "instanceId": "0903d4738bfceb212abe7417386268a386894b588376561033446cb99ecbb6eb"}, "id": "blfnx2v7mPFVCYJL", "tags": []}