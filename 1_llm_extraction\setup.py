"""
Agno LLM 理賠分析應用程式設定腳本
"""

import subprocess
import sys
import os

def install_requirements():
    """安裝必要套件"""
    print("正在安裝必要套件...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        print("✅ 套件安裝成功！")
    except subprocess.CalledProcessError as e:
        print(f"❌ 安裝套件時發生錯誤: {e}")
        return False
    return True

def setup_env_file():
    """設定環境變數檔案"""
    if not os.path.exists(".env"):
        if os.path.exists(".env.example"):
            print("正在從模板建立 .env 檔案...")
            with open(".env.example", "r", encoding="utf-8") as src, open(".env", "w", encoding="utf-8") as dst:
                dst.write(src.read())
            print("✅ .env 檔案建立成功！")
            print("⚠️  請編輯 .env 檔案並添加您的 API 金鑰")
            print("💡 推薦使用 Google Gemini API（成本最低，效能最佳）")
        else:
            print("❌ 找不到 .env.example 檔案")
            return False
    else:
        print("✅ .env 檔案已存在")
    return True

def check_data_files():
    """檢查必要的資料檔案是否存在"""
    required_files = ["raw_data_with_word_count.csv"]
    missing_files = []

    for file in required_files:
        if not os.path.exists(file):
            missing_files.append(file)

    if missing_files:
        print(f"⚠️  缺少必要檔案: {missing_files}")
        print("請確保這些檔案在專案目錄中")
        return False
    else:
        print("✅ 所有必要的資料檔案都已找到")
        print("💡 系統提示詞已內建在應用程式中，無需額外的 JSON 配置檔案")
        return True

def main():
    """主要設定函數"""
    print("設定 Agno LLM 理賠分析應用程式")
    print("=" * 50)

    # 安裝套件
    if not install_requirements():
        return

    # 設定環境變數檔案
    if not setup_env_file():
        return

    # 檢查資料檔案
    check_data_files()

    print("\n" + "=" * 50)
    print("設定完成！")
    print("\n後續步驟:")
    print("1. 編輯 .env 檔案並添加您的 API 金鑰")
    print("2. 執行: python test_single_claim.py (測試)")
    print("3. 執行: python agno_llm_app.py (處理所有理賠案例)")
    print("4. 推薦使用 Google Gemini 2.5 Flash Preview 05-20 模型")
    print("\n📚 詳細說明請參考:")
    print("   • README.md - 完整使用說明")
    print("   • GEMINI_SETUP_GUIDE.md - Gemini API 設定指南")
    print("   • USAGE_GUIDE.md - 詳細使用指南")

if __name__ == "__main__":
    main()
