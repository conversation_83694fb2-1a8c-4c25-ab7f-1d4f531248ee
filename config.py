"""
Agno LLM 理賠分析應用程式配置檔案
"""
import os
from dotenv import load_dotenv

# 載入環境變數
load_dotenv()

# 檔案路徑
CSV_FILE_PATH = "raw_data_with_word_count.csv"
JSON_CONFIG_PATH = "lmm_extraction.json"

# API 金鑰 (請在 .env 檔案中設定)
OPENAI_API_KEY = os.getenv("OPENAI_API_KEY")
ANTHROPIC_API_KEY = os.getenv("ANTHROPIC_API_KEY")
GOOGLE_API_KEY = os.getenv("GOOGLE_API_KEY")

# 模型配置
DEFAULT_MODEL = "gemini-2.5-flash-preview-05-20"  # 預設使用 Gemini 2.5 Flash Preview

# 處理配置
BATCH_SIZE = 10  # 一次處理的理賠案例數量
MAX_RETRIES = 3  # API 呼叫失敗時的最大重試次數

# 將新增到 CSV 的輸出欄位
OUTPUT_COLUMNS = [
    "accident_type",
    "time_of_incident",
    "location_of_incident",
    "self_role",
    "self_damage_status",
    "third_party_count",
    "third_party_details",
    "property_damage_description",
    "personal_injury_description",
    "incident_narrative",
    "potential_liability_third_party_info",
    "reporting_medical_status",
    "environmental_factors",
    "other_supporting_documents_mentioned",
    "emotional_tone",
    "severity_attitude",
    "case_complexity",
    "case_complexity_description"
]
