"""
Configuration file for the Agno LLM Claims Analysis Application
"""
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# File paths
CSV_FILE_PATH = "raw_data_with_word_count.csv"
JSON_CONFIG_PATH = "lmm_extraction.json"

# API Keys (set these in your .env file)
OPENAI_API_KEY = os.getenv("OPENAI_API_KEY")
ANTHROPIC_API_KEY = os.getenv("ANTHROPIC_API_KEY")

# Model configuration
DEFAULT_MODEL = "gpt-4o"  # Can be changed to "claude-3-sonnet-20240229" if using Anthropic

# Processing configuration
BATCH_SIZE = 10  # Number of claims to process at once
MAX_RETRIES = 3  # Maximum number of retries for failed API calls

# Output columns that will be added to the CSV
OUTPUT_COLUMNS = [
    "accident_type",
    "time_of_incident", 
    "location_of_incident",
    "self_role",
    "self_damage_status",
    "third_party_count",
    "third_party_details",
    "property_damage_description",
    "personal_injury_description",
    "incident_narrative",
    "potential_liability_third_party_info",
    "reporting_medical_status",
    "environmental_factors",
    "other_supporting_documents_mentioned",
    "emotional_tone",
    "severity_attitude",
    "case_complexity",
    "case_complexity_description"
]
