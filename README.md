# Agno LLM Claims Analysis Application

這是一個使用 Agno framework 建立的 LLM 應用程式，用於從保險理賠描述中提取關鍵特徵。

## 功能特色

- 使用 Agno framework 建立高效能的 LLM agent
- 支援 OpenAI GPT 和 Anthropic Claude 模型
- 批次處理大量理賠數據
- 自動提取 17 個關鍵特徵欄位
- 支援斷點續傳，避免重複處理
- 完整的錯誤處理和日誌記錄

## 安裝步驟

1. **安裝依賴套件**
```bash
pip install -r requirements.txt
```

2. **設定 API 金鑰**
```bash
# 複製環境變數模板
cp .env.example .env

# 編輯 .env 文件，添加您的 API 金鑰
# 您只需要設定其中一個 API 金鑰即可
```

3. **準備數據文件**
確保以下文件存在於專案目錄中：
- `raw_data_with_word_count.csv` - 包含理賠描述的 CSV 文件
- `lmm_extraction.json` - 包含 agent 提示詞的配置文件

## 使用方法

### 1. 測試單一理賠分析
```bash
python test_single_claim.py
```

### 2. 批次處理所有理賠
```bash
python agno_llm_app.py
```

## 提取的特徵欄位

應用程式會從每個理賠描述中提取以下 17 個欄位：

1. **accident_type** - 事故類型
2. **time_of_incident** - 事故發生時間
3. **location_of_incident** - 事故發生地點
4. **self_role** - 投保人角色
5. **self_damage_status** - 自身損害狀態
6. **third_party_count** - 第三方數量
7. **third_party_details** - 第三方詳情
8. **property_damage_description** - 財產損害描述
9. **personal_injury_description** - 人身傷害描述
10. **incident_narrative** - 事故經過
11. **potential_liability_third_party_info** - 潛在責任和第三方資訊
12. **reporting_medical_status** - 報案/醫療狀態
13. **environmental_factors** - 環境因素
14. **other_supporting_documents_mentioned** - 其他支持文件
15. **emotional_tone** - 情緒語調
16. **severity_attitude** - 嚴重性態度
17. **case_complexity** - 案件複雜度
18. **case_complexity_description** - 複雜度描述

## 配置選項

在 `config.py` 中可以調整以下設定：

- `DEFAULT_MODEL` - 預設使用的 LLM 模型
- `BATCH_SIZE` - 批次處理大小
- `MAX_RETRIES` - 最大重試次數

## 支援的模型

- **OpenAI**: gpt-4o, gpt-4-turbo, gpt-3.5-turbo
- **Anthropic**: claude-3-sonnet-20240229, claude-3-haiku-20240307

## 注意事項

1. **API 費用**: 使用 LLM API 會產生費用，請注意您的使用量
2. **速率限制**: 應用程式包含延遲機制以避免觸發 API 速率限制
3. **數據備份**: 建議在處理前備份原始 CSV 文件
4. **斷點續傳**: 應用程式會自動跳過已處理的理賠，可以安全地重新執行

## 故障排除

### 常見問題

1. **API 金鑰錯誤**
   - 確保在 `.env` 文件中正確設定了 API 金鑰
   - 檢查金鑰是否有效且有足夠的額度

2. **文件找不到**
   - 確保 `raw_data_with_word_count.csv` 和 `lmm_extraction.json` 在正確位置

3. **記憶體不足**
   - 減少 `BATCH_SIZE` 的值
   - 確保系統有足夠的可用記憶體

## 專案結構

```
├── agno_llm_app.py          # 主要應用程式
├── config.py                # 配置文件
├── utils.py                 # 工具函數
├── test_single_claim.py     # 單一理賠測試
├── requirements.txt         # 依賴套件
├── .env.example            # 環境變數模板
├── README.md               # 說明文件
├── raw_data_with_word_count.csv  # 輸入數據
└── lmm_extraction.json     # Agent 配置
```

## 授權

本專案使用 MIT 授權條款。
