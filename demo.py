"""
展示 Agno LLM 理賠分析應用程式運作方式的演示腳本
此腳本展示關鍵功能，無需 API 金鑰
"""

import json
import pandas as pd
from typing import Dict, Any

def demo_extract_features():
    """
    使用範例資料展示特徵提取過程
    """
    print("🚀 Agno LLM 理賠分析應用程式演示")
    print("=" * 60)

    # 來自 CSV 的範例理賠描述
    sample_claim = "driving northbound center lane cars stopped front slammed brakes truck jack knifed right struck stationary vehicles side road earlier accident tractor heavy damage trailer slightdamage passenger side deadlined yard shop eval morning injuries reported accident per beavercreek police scene driver also ok refused treatment injuries"

    print(f"📝 範例理賠描述:")
    print(f"   {sample_claim}")
    print("\n" + "=" * 60)
    
    # 預期的輸出結構（LLM 會提取的內容）
    expected_output = {
        "accident_type": "車輛事故",
        "time_of_incident": "早上",
        "location_of_incident": "北向中央車道",
        "self_role": "駕駛",
        "self_damage_status": "財產受損",
        "third_party_count": "多方",
        "third_party_details": "靜止車輛、側翻的卡車",
        "property_damage_description": "拖車頭嚴重損壞，拖車乘客側輕微損壞",
        "personal_injury_description": "駕駛拒絕接受傷害治療",
        "incident_narrative": "駕駛在中央車道北向行駛時前方車輛停止，緊急煞車，卡車向右側翻並撞擊路邊靜止車輛",
        "potential_liability_third_party_info": "路邊先前發生事故，警方在現場",
        "reporting_medical_status": "已報警，駕駛拒絕醫療治療",
        "environmental_factors": None,
        "other_supporting_documents_mentioned": "Beavercreek 警方的警察報告",
        "emotional_tone": "中性",
        "severity_attitude": "中性",
        "case_complexity": "複雜",
        "case_complexity_description": "涉及多車碰撞及先前事故，需要詳細調查的警方回應"
    }

    print("🎯 預期提取的特徵:")
    print(json.dumps(expected_output, indent=2, ensure_ascii=False))
    print("\n" + "=" * 60)
    
    return expected_output

def demo_csv_processing():
    """
    展示 CSV 處理的運作方式
    """
    print("📊 CSV 處理演示")
    print("=" * 60)

    # 載入 CSV 資料的小樣本
    try:
        df = pd.read_csv("raw_data_with_word_count.csv")
        print(f"✅ 已載入 CSV，共 {len(df)} 筆理賠案例")

        # 顯示前幾列
        print("\n📋 原始資料樣本:")
        print(df[['claim_description', 'coverage_code', 'accident_source']].head(3).to_string())

        # 顯示將要新增的欄位
        new_columns = [
            "accident_type", "time_of_incident", "location_of_incident",
            "self_role", "self_damage_status", "third_party_count",
            "third_party_details", "property_damage_description",
            "personal_injury_description", "incident_narrative",
            "potential_liability_third_party_info", "reporting_medical_status",
            "environmental_factors", "other_supporting_documents_mentioned",
            "emotional_tone", "severity_attitude", "case_complexity",
            "case_complexity_description", "llm_processed"
        ]

        print(f"\n🆕 將新增的欄位 ({len(new_columns)} 個):")
        for i, col in enumerate(new_columns, 1):
            print(f"   {i:2d}. {col}")

    except FileNotFoundError:
        print("❌ 找不到 CSV 檔案。請確保 'raw_data_with_word_count.csv' 在目前目錄中。")
    except Exception as e:
        print(f"❌ 載入 CSV 時發生錯誤: {e}")

def demo_system_prompt():
    """
    顯示將使用的系統提示詞
    """
    print("\n🤖 系統提示詞演示")
    print("=" * 60)

    try:
        with open("lmm_extraction.json", 'r', encoding='utf-8') as f:
            json_data = json.load(f)

        # 提取系統訊息
        for node in json_data.get('nodes', []):
            if node.get('name') == 'Claims Feature Extraction Expert':
                parameters = node.get('parameters', {})
                options = parameters.get('options', {})
                system_message = options.get('systemMessage', '')
                if system_message.startswith('='):
                    system_message = system_message[1:]

                print("📜 系統提示詞 (前 500 個字元):")
                print(f"   {system_message[:500]}...")
                print(f"\n📏 提示詞總長度: {len(system_message)} 個字元")
                break
        else:
            print("❌ 在 JSON 檔案中找不到系統提示詞")

    except FileNotFoundError:
        print("❌ 找不到 JSON 配置檔案")
    except Exception as e:
        print(f"❌ 載入 JSON 時發生錯誤: {e}")

def demo_workflow():
    """
    顯示完整的工作流程
    """
    print("\n🔄 完整工作流程演示")
    print("=" * 60)

    workflow_steps = [
        "1. 📖 載入包含理賠描述的 CSV 資料",
        "2. 🔧 初始化提取特徵的新欄位",
        "3. 🤖 使用專門提示詞建立 Agno LLM 代理程式",
        "4. 🔍 識別未處理的理賠案例",
        "5. 📦 批次處理理賠案例以避免速率限制",
        "6. 🧠 對每個理賠案例：發送到 LLM → 提取特徵 → 解析 JSON",
        "7. 💾 使用提取的特徵更新 DataFrame",
        "8. 💿 每批次後儲存進度",
        "9. ✅ 標記理賠案例為已處理以啟用續傳功能",
        "10. 🎉 完成處理並儲存最終結果"
    ]

    for step in workflow_steps:
        print(f"   {step}")

    print(f"\n⚡ 主要功能:")
    print(f"   • 批次處理提高效率")
    print(f"   • 續傳功能（跳過已處理的案例）")
    print(f"   • 速率限制避免 API 限制")
    print(f"   • 全面的錯誤處理")
    print(f"   • 每批次後儲存進度")
    print(f"   • 預設使用 Gemini 2.5 Flash Preview 05-20 模型")

def main():
    """
    執行完整演示
    """
    demo_extract_features()
    demo_csv_processing()
    demo_system_prompt()
    demo_workflow()

    print("\n" + "=" * 60)
    print("🚀 準備執行實際應用程式！")
    print("📋 後續步驟:")
    print("   1. 安裝依賴套件: pip install -r requirements.txt")
    print("   2. 在 .env 檔案中設定 API 金鑰")
    print("   3. 測試: python test_single_claim.py")
    print("   4. 執行完整處理: python agno_llm_app.py")
    print("   5. 預設使用 Gemini 2.5 Flash Preview 05-20 模型")
    print("=" * 60)

if __name__ == "__main__":
    main()
