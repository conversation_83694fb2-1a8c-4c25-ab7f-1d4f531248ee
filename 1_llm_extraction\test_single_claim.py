"""
使用 Agno LLM 應用程式分析單一理賠描述的測試腳本
"""

import json
from agno_llm_app import ClaimsAnalysisAgent
import config

def test_single_claim():
    """
    使用單一範例測試理賠分析
    """
    # 來自您的 CSV 的範例理賠描述
    test_claim = "driving northbound center lane cars stopped front slammed brakes truck jack knifed right struck stationary vehicles side road earlier accident tractor heavy damage trailer slightdamage passenger side deadlined yard shop eval morning injuries reported accident per beavercreek police scene driver also ok refused treatment injuries"

    print("測試 Agno LLM 理賠分析")
    print("=" * 50)
    print(f"測試理賠案例: {test_claim}")
    print("=" * 50)
    print(f"使用模型: {config.DEFAULT_MODEL}")
    print("=" * 50)

    try:
        # 建立代理程式
        agent = ClaimsAnalysisAgent(model_name=config.DEFAULT_MODEL)

        # 分析理賠案例
        result = agent.analyze_claim(test_claim)

        # 顯示結果
        print("提取的特徵:")
        print(json.dumps(result, indent=2, ensure_ascii=False))

        # 檢查是否存在所有預期欄位
        expected_fields = config.OUTPUT_COLUMNS
        missing_fields = [field for field in expected_fields if field not in result]

        if missing_fields:
            print(f"\n缺少的欄位: {missing_fields}")
        else:
            print("\n✅ 所有預期欄位都成功提取！")

        # 顯示統計資訊
        print(f"\n📊 統計資訊:")
        print(f"   • 預期欄位數量: {len(expected_fields)}")
        print(f"   • 實際提取欄位數量: {len(result)}")
        print(f"   • 成功率: {(len(result) - len(missing_fields)) / len(expected_fields) * 100:.1f}%")

    except Exception as e:
        print(f"❌ 測試期間發生錯誤: {e}")
        print("\n💡 請檢查:")
        print("   1. API 金鑰是否正確設定在 .env 檔案中")
        print("   2. 網路連線是否正常")
        print("   3. 依賴套件是否正確安裝")

if __name__ == "__main__":
    test_single_claim()
